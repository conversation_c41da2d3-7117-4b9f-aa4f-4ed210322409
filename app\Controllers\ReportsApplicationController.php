<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ExerciseModel;
use App\Models\PositionsModel;
use App\Models\AppxApplicationDetailsModel;

/**
 * ReportsApplicationController
 *
 * Controller for application-related reports including application register and pre-screening reports
 */
class ReportsApplicationController extends Controller
{
    protected $exerciseModel;
    protected $positionsModel;
    protected $applicationModel;

    public function __construct()
    {
        helper(['url', 'form']);
        $this->exerciseModel = new ExerciseModel();
        $this->positionsModel = new PositionsModel();
        $this->applicationModel = new AppxApplicationDetailsModel();
    }

    /**
     * [GET] Application Register Report
     * URI: /reports/application-register/{exerciseId}
     */
    public function applicationRegister($exerciseId)
    {
        try {
            // Get exercise data
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get all applications for this exercise
            $applications = $this->applicationModel->where('exercise_id', $exerciseId)->findAll();

            // Get all positions for this exercise
            $positions = $this->positionsModel->select('positions.*')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->where('positions_groups.exercise_id', $exerciseId)
                ->where('positions.deleted_at IS NULL')
                ->findAll();

            // Calculate statistics
            $totalApplications = count($applications);
            $totalApplicants = count(array_unique(array_column($applications, 'applicant_id')));
            $positionsWithApplications = 0;
            $positionsWithoutApplications = 0;

            foreach ($positions as $position) {
                $hasApplications = false;
                foreach ($applications as $application) {
                    if ($application['position_id'] == $position['id']) {
                        $hasApplications = true;
                        break;
                    }
                }
                if ($hasApplications) {
                    $positionsWithApplications++;
                } else {
                    $positionsWithoutApplications++;
                }
            }

            $statistics = [
                'total_applications' => $totalApplications,
                'total_applicants' => $totalApplicants,
                'total_positions_with_applications' => $positionsWithApplications,
                'total_positions_without_applications' => $positionsWithoutApplications
            ];

            $data = [
                'title' => 'Application Register Report - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'applications' => $applications,
                'statistics' => $statistics
            ];

            return view('application_reports/appx_reports_application_register', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching application register data: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading application register');
        }
    }

    /**
     * [GET] Pre-Screening Report
     * URI: /reports/pre-screening/{exerciseId}
     */
    public function preScreening($exerciseId)
    {
        // Get exercise data
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Exercise not found');
        }

        // Get pre-screened applications for this exercise
        $preScreeningResults = $this->applicationModel->getPreScreenedApplicationsForReport($exerciseId, $exercise['org_id']);

        $data = [
            'title' => 'Pre-Screening Report - ' . $exercise['exercise_name'],
            'menu' => 'reports',
            'exercise' => $exercise,
            'pre_screening_results' => $preScreeningResults
        ];

        return view('application_reports/appx_reports_pre_screening', $data);
    }

    /**
     * [GET] Pre-Screening Detail Report
     * URI: /reports/pre-screening-detail/{applicationId}
     */
    public function preScreeningDetail($applicationId)
    {
        // Get application with full details including screened by username
        $application = $this->applicationModel->select('
                appx_application_details.*,
                exercises.exercise_name,
                exercises.gazzetted_no,
                exercises.gazzetted_date,
                exercises.advertisement_no,
                exercises.advertisement_date,
                exercises.is_internal,
                exercises.mode_of_advertisement,
                exercises.publish_date_from,
                exercises.publish_date_to,
                exercises.description,
                exercises.pre_screen_criteria,
                exercises.applicants_information,
                exercises.applicants_notice,
                exercises.status as exercise_status,
                dakoii_org.org_name,
                dakoii_org.org_code,
                users.username as screened_by_username
            ')
            ->join('exercises', 'appx_application_details.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
            ->join('users', 'appx_application_details.pre_screened_by = users.id', 'left')
            ->where('appx_application_details.id', $applicationId)
            ->first();

        if (!$application) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Application not found');
        }

        $data = [
            'title' => 'Pre-Screening Detail - ' . $application['first_name'] . ' ' . $application['last_name'],
            'menu' => 'reports',
            'application' => $application
        ];

        return view('application_reports/appx_reports_pre_screening_detail', $data);
    }

    /**
     * [GET] Application Register Report by Position
     * URI: /reports/application-register-position/{positionId}
     */
    public function applicationRegisterByPosition($positionId)
    {
        try {
            // Get position data with exercise and organization details
            $position = $this->positionsModel->select('
                    positions.*,
                    positions_groups.group_name,
                    positions_groups.exercise_id,
                    exercises.exercise_name,
                    exercises.advertisement_no,
                    dakoii_org.org_name
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
                ->where('positions.id', $positionId)
                ->first();

            if (!$position) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Position not found');
            }

            // Get exercise data
            $exercise = $this->exerciseModel->find($position['exercise_id']);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get all applications for this specific position
            $applications = $this->applicationModel->select('
                    appx_application_details.*,
                    CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name
                ')
                ->where('position_id', $positionId)
                ->orderBy('appx_application_details.created_at', 'DESC')
                ->findAll();

            // Calculate statistics
            $totalApplications = count($applications);
            $totalApplicants = count(array_unique(array_column($applications, 'applicant_id')));

            $statistics = [
                'total_applications' => $totalApplications,
                'total_applicants' => $totalApplicants
            ];

            $data = [
                'title' => 'Application Register - ' . $position['designation'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'position' => $position,
                'applications' => $applications,
                'statistics' => $statistics
            ];

            return view('application_reports/appx_reports_application_register_position', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching position application register data: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading position application register');
        }
    }

    /**
     * [GET] Positions with Applications Report
     * URI: /reports/positions-with-applications/{exerciseId}
     */
    public function positionsWithApplications($exerciseId)
    {
        try {
            // Get exercise data
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get all positions for this exercise
            $positions = $this->positionsModel->select('
                    positions.*,
                    positions_groups.group_name,
                    dakoii_org.org_name,
                    COUNT(appx_application_details.id) as application_count
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
                ->join('appx_application_details', 'positions.id = appx_application_details.position_id', 'left')
                ->where('positions_groups.exercise_id', $exerciseId)
                ->where('(positions.status = 1 OR positions.status = "active")')
                ->groupBy('positions.id')
                ->having('application_count > 0')
                ->orderBy('positions.designation', 'ASC')
                ->findAll();

            $data = [
                'title' => 'Positions with Applications - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'positions' => $positions
            ];

            return view('application_reports/appx_reports_positions_with_applications', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching positions with applications: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading positions with applications');
        }
    }

    /**
     * [GET] Positions without Applications Report
     * URI: /reports/positions-without-applications/{exerciseId}
     */
    public function positionsWithoutApplications($exerciseId)
    {
        try {
            // Get exercise data
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get all positions for this exercise that have no applications
            $positions = $this->positionsModel->select('
                    positions.*,
                    positions_groups.group_name,
                    dakoii_org.org_name,
                    COUNT(appx_application_details.id) as application_count
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
                ->join('appx_application_details', 'positions.id = appx_application_details.position_id', 'left')
                ->where('positions_groups.exercise_id', $exerciseId)
                ->where('(positions.status = 1 OR positions.status = "active")')
                ->groupBy('positions.id')
                ->having('application_count = 0')
                ->orderBy('positions.designation', 'ASC')
                ->findAll();

            $data = [
                'title' => 'Positions without Applications - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'positions' => $positions
            ];

            return view('application_reports/appx_reports_positions_without_applications', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching positions without applications: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading positions without applications');
        }
    }

    /**
     * [POST] Export Application Register Report
     * URI: /reports/application-register/export
     */
    public function exportApplicationRegister()
    {
        // Mock export response for UI development
        $response = [
            'success' => true,
            'message' => 'Application register report exported successfully',
            'file_url' => base_url('exports/application_register_' . date('Y-m-d_H-i-s') . '.xlsx')
        ];

        return $this->response->setJSON($response);
    }

    /**
     * [POST] Export Pre-Screening Report as PDF
     * URI: /reports/pre-screening/export
     */
    public function exportPreScreening()
    {
        $exerciseId = $this->request->getPost('exercise_id');

        if (!$exerciseId) {
            log_message('error', 'PDF Export: Exercise ID not provided. POST data: ' . json_encode($this->request->getPost()));
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise ID is required'
            ]);
        }

        try {
            // Get exercise data with organization info
            $exercise = $this->exerciseModel->select('
                    exercises.*,
                    dakoii_org.org_name,
                    dakoii_org.org_code
                ')
                ->join('dakoii_org', 'exercises.org_id = dakoii_org.id', 'left')
                ->where('exercises.id', $exerciseId)
                ->first();

            if (!$exercise) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Exercise not found'
                ]);
            }

            // Get pre-screened applications for this exercise
            $preScreeningResults = $this->applicationModel->getPreScreenedApplicationsForReport($exerciseId, $exercise['org_id']);

            log_message('info', 'PDF Export: Exercise data: ' . json_encode($exercise));
            log_message('info', 'PDF Export: Found ' . count($preScreeningResults) . ' pre-screening results');

            // Generate PDF
            $pdfPath = $this->generatePreScreeningPDF($exercise, $preScreeningResults);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Pre-screening report exported successfully',
                'file_url' => base_url('reports/download-pdf/' . basename($pdfPath))
            ]);

        } catch (\Exception $e) {
            log_message('error', 'PDF Export Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * [GET] Download PDF file
     * URI: /reports/download-pdf/{filename}
     */
    public function downloadPdf($filename)
    {
        $filepath = FCPATH . 'exports/' . $filename;

        if (!file_exists($filepath)) {
            return redirect()->back()->with('error', 'File not found');
        }

        // Force download
        return $this->response->download($filepath, null);
    }

    /**
     * Generate Pre-Screening Report PDF
     */
    private function generatePreScreeningPDF($exercise, $preScreeningResults)
    {
        log_message('info', 'PDF Generation: Starting PDF generation');

        try {
            // Create custom PDF class with footer
            $pdf = new class('L', 'mm', 'A4') extends \FPDF {
                function Footer() {
                    // Position at 15 mm from bottom
                    $this->SetY(-15);

                    // Add a line separator
                    $this->SetDrawColor(200, 200, 200);
                    $this->Line(10, $this->GetY(), 287, $this->GetY());

                    // Footer content
                    $this->SetFont('Arial', '', 8);
                    $this->SetTextColor(100, 100, 100);

                    // Move down a bit after the line
                    $this->SetY($this->GetY() + 2);

                    // Left side - System info
                    $this->SetX(10);
                    $this->Cell(90, 4, 'Generated by ' . SYSTEM_NAME . ' ' . SYSTEM_VERSION, 0, 0, 'L');

                    // Center - Organization info
                    $this->SetX(100);
                    $this->Cell(87, 4, 'Dakoii Echad Recruitment & Selection System', 0, 0, 'C');

                    // Right side - Date and page
                    $this->SetX(187);
                    $this->Cell(90, 4, 'Generated on: ' . date('M d, Y H:i') . ' | Page ' . $this->PageNo(), 0, 0, 'R');

                    // Second line
                    $this->SetY($this->GetY() + 4);
                    $this->SetX(10);
                    $this->Cell(267, 4, 'Developed by Dakoii Systems & Echad Consultancy Services', 0, 0, 'C');

                    // Reset text color
                    $this->SetTextColor(0, 0, 0);
                }
            };

            $pdf->AddPage();
            $pdf->SetFont('Arial', 'B', 16);

            // Title
            $pdf->Cell(0, 10, 'Pre-Screening Report', 0, 1, 'C');
            $pdf->Ln(5);

            // Exercise Information
            $pdf->SetFont('Arial', 'B', 12);
            $pdf->Cell(0, 8, 'Exercise Information', 0, 1);
            $pdf->SetFont('Arial', '', 10);
            $pdf->Cell(40, 6, 'Exercise Name:', 0, 0);
            $pdf->Cell(0, 6, $exercise['exercise_name'] ?? 'N/A', 0, 1);
            $pdf->Cell(40, 6, 'Advertisement No:', 0, 0);
            $pdf->Cell(0, 6, $exercise['advertisement_no'] ?? 'N/A', 0, 1);
            $pdf->Cell(40, 6, 'Organization:', 0, 0);
            $pdf->Cell(0, 6, $exercise['org_name'] ?? 'N/A', 0, 1);
            $pdf->Cell(40, 6, 'Generated On:', 0, 0);
            $pdf->Cell(0, 6, date('M d, Y H:i'), 0, 1);
            $pdf->Ln(10);

            // Summary Statistics
            $totalApplications = count($preScreeningResults);
            $passedCount = 0;
            $failedCount = 0;

            foreach ($preScreeningResults as $result) {
                if ($result['pre_screened_status'] === 'passed') {
                    $passedCount++;
                } else {
                    $failedCount++;
                }
            }

            $pdf->SetFont('Arial', 'B', 12);
            $pdf->Cell(0, 8, 'Summary Statistics', 0, 1);
            $pdf->SetFont('Arial', '', 10);
            $pdf->Cell(40, 6, 'Total Applications:', 0, 0);
            $pdf->Cell(0, 6, $totalApplications, 0, 1);
            $pdf->Cell(40, 6, 'Passed:', 0, 0);
            $pdf->Cell(0, 6, $passedCount, 0, 1);
            $pdf->Cell(40, 6, 'Failed:', 0, 0);
            $pdf->Cell(0, 6, $failedCount, 0, 1);
            $pdf->Ln(10);

            // Applications Table Header
            $pdf->SetFont('Arial', 'B', 12);
            $pdf->Cell(0, 8, 'Pre-Screening Results', 0, 1);

            // Enhanced table headers with position names and detailed criteria results
            $pdf->SetFont('Arial', 'B', 7);
            $pdf->Cell(8, 8, '#', 1, 0, 'C');
            $pdf->Cell(30, 8, 'Applicant Name', 1, 0, 'C');
            $pdf->Cell(25, 8, 'Application No', 1, 0, 'C');
            $pdf->Cell(18, 8, 'Position Ref', 1, 0, 'C');
            $pdf->Cell(35, 8, 'Position Title', 1, 0, 'C');
            $pdf->Cell(12, 8, 'Gender', 1, 0, 'C');
            $pdf->Cell(18, 8, 'Status', 1, 0, 'C');
            $pdf->Cell(25, 8, 'Screened Date', 1, 0, 'C');
            $pdf->Cell(97, 8, 'Criteria Results Details', 1, 1, 'C');

            // Table data
            $pdf->SetFont('Arial', '', 6);
            $rowNum = 1;

            foreach ($preScreeningResults as $result) {
                // Check if we need a new page (adjusted for landscape, leaving space for footer)
                if ($pdf->GetY() > 170) {
                    $pdf->AddPage();
                    // Repeat headers
                    $pdf->SetFont('Arial', 'B', 7);
                    $pdf->Cell(8, 8, '#', 1, 0, 'C');
                    $pdf->Cell(30, 8, 'Applicant Name', 1, 0, 'C');
                    $pdf->Cell(25, 8, 'Application No', 1, 0, 'C');
                    $pdf->Cell(18, 8, 'Position Ref', 1, 0, 'C');
                    $pdf->Cell(35, 8, 'Position Title', 1, 0, 'C');
                    $pdf->Cell(12, 8, 'Gender', 1, 0, 'C');
                    $pdf->Cell(18, 8, 'Status', 1, 0, 'C');
                    $pdf->Cell(25, 8, 'Screened Date', 1, 0, 'C');
                    $pdf->Cell(97, 8, 'Criteria Results Details', 1, 1, 'C');
                    $pdf->SetFont('Arial', '', 6);
                }

                // Parse criteria results for detailed display with proper line breaks
                $criteriaResultsDetails = 'N/A';
                if (!empty($result['pre_screened_criteria_results'])) {
                    $criteriaData = json_decode($result['pre_screened_criteria_results'], true);
                    if ($criteriaData && isset($criteriaData['criteria_evaluations'])) {
                        $detailsArray = [];
                        foreach ($criteriaData['criteria_evaluations'] as $evaluation) {
                            $criteriaName = isset($evaluation['criteria_name']) ? $evaluation['criteria_name'] : 'Unknown';
                            $evaluationResult = isset($evaluation['evaluation_result']) ? strtoupper($evaluation['evaluation_result']) : 'N/A';

                            // Create full criterion line first
                            $fullLine = $criteriaName . ': ' . $evaluationResult;

                            // Only wrap if the line is longer than the cell can accommodate (approximately 60 characters for 97mm width)
                            if (strlen($fullLine) > 60) {
                                // Wrap the criteria name only, keeping the result on the same line as the last part
                                $wrappedCriteria = wordwrap($criteriaName, 50, "\n", false);
                                $detailsArray[] = $wrappedCriteria . ': ' . $evaluationResult;
                            } else {
                                $detailsArray[] = $fullLine;
                            }
                        }
                        $criteriaResultsDetails = implode("\n", $detailsArray);
                    }
                }

                // Calculate row height based on criteria results content
                $lineCount = substr_count($criteriaResultsDetails, "\n") + 1;
                $rowHeight = max(6, $lineCount * 4); // Minimum 6mm, 4mm per line

                // Store current Y position
                $currentY = $pdf->GetY();

                // Draw cells with consistent height
                $pdf->Cell(8, $rowHeight, $rowNum, 1, 0, 'C');
                $pdf->Cell(30, $rowHeight, substr($result['full_name'], 0, 18), 1, 0);
                $pdf->Cell(25, $rowHeight, substr($result['application_number'], 0, 12), 1, 0);
                $pdf->Cell(18, $rowHeight, substr($result['position_reference'] ?? 'N/A', 0, 8), 1, 0);
                $pdf->Cell(35, $rowHeight, substr($result['position_title'] ?? 'N/A', 0, 22), 1, 0);
                $pdf->Cell(12, $rowHeight, substr($result['gender'], 0, 1), 1, 0, 'C');
                $pdf->Cell(18, $rowHeight, ucfirst($result['pre_screened_status']), 1, 0, 'C');
                $pdf->Cell(25, $rowHeight, date('M d, Y', strtotime($result['pre_screened_at'])), 1, 0, 'C');

                // Position for multi-cell criteria results
                // X position = 10 (left margin) + sum of all previous column widths (8+30+25+18+35+12+18+25 = 171)
                $pdf->SetXY(181, $currentY);
                $pdf->MultiCell(97, 4, $criteriaResultsDetails, 1, 'L');

                // Move to next row
                $pdf->SetY($currentY + $rowHeight);

                $rowNum++;
            }

            // Create exports directory if it doesn't exist
            $exportsDir = FCPATH . 'exports';
            if (!is_dir($exportsDir)) {
                if (!mkdir($exportsDir, 0755, true)) {
                    throw new \Exception('Failed to create exports directory');
                }
            }

            // Generate filename and save
            $filename = 'pre_screening_report_' . str_replace(' ', '_', $exercise['exercise_name']) . '_' . date('Y-m-d_H-i-s') . '.pdf';
            $filepath = $exportsDir . '/' . $filename;

            log_message('info', 'PDF Generation: Saving PDF to: ' . $filepath);

            $pdf->Output('F', $filepath);

            // Verify file was created
            if (!file_exists($filepath)) {
                throw new \Exception('PDF file was not created successfully');
            }

            log_message('info', 'PDF Generation: PDF saved successfully. File size: ' . filesize($filepath) . ' bytes');

            return 'exports/' . $filename;

        } catch (\Exception $e) {
            log_message('error', 'PDF Generation Error: ' . $e->getMessage());
            throw $e;
        }
    }

}
